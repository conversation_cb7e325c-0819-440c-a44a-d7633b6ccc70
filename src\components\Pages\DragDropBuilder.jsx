import React, { useState, useEffect, useRef, useCallback } from "react";
import { Tooltip, Collapse, Input, Spin } from "antd";
import Header from "../Layout/Header";
import {
  Save,
  X,
  Eye,
  Smartphone,
  Tablet,
  Monitor,
  Settings,
  Trash2,
  GripVertical,
  ChevronLeft,
  ChevronUp,
  ChevronDown,
  Search,
  Loader2,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import PageStructure from "./Component/PageStructure";
import PageSetting from "./Component/PageSetting";
import PagePreview from "./Component/PagePreview";
import { DND_TYPES } from "../../util/content";
import { generateGlobalPreviewHTML } from "../Components/content";

const DragDropBuilder = ({ page, onSave, onCancel }) => {
  const api = useHttp();
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pageData, setPageData] = useState({
    name: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    custom_css: "",
    custom_js: "",
    components: [],
  });

  const [saving, setSaving] = useState(false);
  const [isStructureOpen, setIsStructureOpen] = useState(true);
  const [isComponentOpen, setIsComponentOpen] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // Debouncing effect for search
  useEffect(() => {
    if (searchTerm) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
        setIsSearching(false);
      }, 300); // 300ms debounce delay

      return () => {
        clearTimeout(timer);
      };
    } else {
      setDebouncedSearchTerm("");
      setIsSearching(false);
    }
  }, [searchTerm]);

  // Responsive detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);

      // Auto-close sidebars on mobile for better UX
      if (width < 768) {
        // On mobile, close both sidebars by default
        setIsComponentOpen(false);
        setIsStructureOpen(false);
      } else if (width >= 768 && width < 1024) {
        // On tablet, allow one sidebar open
        // Keep current state but ensure at least center content is visible
      }
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      console.log("Components fetched:", res);
      setComponents(res);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories fetched:", res);
      setCategories(res);
    });

    if (page) {
      // Ensure existing components have uniqueId for stable React keys
      const componentsWithUniqueId = (page.components || []).map(
        (comp, index) => ({
          ...comp,
          uniqueId:
            comp.uniqueId ||
            `${comp.id}-${index}-${Date.now()}-${Math.random()
              .toString(36)
              .substring(2, 11)}`,
        })
      );

      setPageData({
        name: page.name || "",
        slug: page.slug || "",
        meta_title: page.meta_title || "",
        meta_description: page.meta_description || "",
        custom_css: page.custom_css || "",
        custom_js: page.custom_js || "",
        components: componentsWithUniqueId,
      });
    }
  }, [page]);

  const handleSave = async () => {
    setSaving(true);

    const apiConfig = page
      ? apiGenerator(CONSTANTS.API.pages.update, { id: page.id })
      : CONSTANTS.API.pages.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Page saved successfully:", res);
        setSaving(false);
        onSave();
      },
      pageData,
      page ? "Page updated successfully!" : "Page created successfully!",
      (error) => {
        console.error("Error saving page:", error);
        setSaving(false);
      }
    );
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...pageData.components];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  const removeComponentFromPage = (index) => {
    console.log("Removing component at index:", index);
    const updatedComponents = pageData.components.filter((_, i) => i !== index);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  // Update CSS class for a specific component in page structure
  const handleCssChange = (index, value) => {
    const updated = [...pageData.components];
    updated[index] = { ...updated[index], cssClass: value };

    setPageData((prevData) => ({
      ...prevData,
      components: updated,
    }));
  };

  const groupedComponents = categories.reduce((acc, category) => {
    const categoryComponents = components.filter(
      (comp) => comp.category_id === category.id
    );
    const filteredComponents = debouncedSearchTerm
      ? categoryComponents.filter((comp) =>
          comp.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        )
      : categoryComponents;

    if (filteredComponents.length > 0) {
      acc[category.id] = {
        name: category.name,
        color: category.color,
        components: filteredComponents,
      };
    }
    return acc;
  }, {});

  // Draggable Component Library item - NO debouncing, immediate drag
  const LibraryItem = ({ comp }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: DND_TYPES.LIB_ITEM,
        item: () => {
          console.log("Starting library component drag:", comp.name);
          return { component: comp };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
        end: (_, monitor) => {
          if (monitor.didDrop()) {
            console.log("Library component dropped successfully:", comp.name);
          } else {
            console.log("Library component drag cancelled:", comp.name);
          }
        },
      }),
      [comp]
    );

    const previewContent = comp?.html_content ? (
      <div className="tw-bg-white tw-rounded-lg tw-border tw-border-gray-200 tw-p-2">
        <div
          className="tw-bg-white tw-rounded tw-shadow-sm tw-overflow-hidden"
          style={{
            width: "120px",
            height: "240px",
          }}
        >
          <iframe
            srcDoc={generateGlobalPreviewHTML({
              type: "component",
              data: [comp],
              title: "Component Preview",
            })}
            className="tw-border-0"
            title="Component Preview"
            style={{
              width: "120px",
              height: "240px",
              pointerEvents: "none",
              overflow: "hidden",
              border: "none",
            }}
          />
        </div>
      </div>
    ) : (
      <div className="tw-flex tw-items-center tw-justify-center tw-h-20 tw-bg-white tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300">
        <div className="tw-text-center">
          <Eye className="tw-w-6 tw-h-6 tw-text-gray-400 tw-mx-auto tw-mb-1" />
          <p className="tw-text-xs tw-text-gray-500">No preview available</p>
        </div>
      </div>
    );

    return (
      <div className="tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-hover:tw-border-gray-300 tw-transition-colors">
        {/* Main component card */}
        <div
          ref={drag}
          className="tw-p-3 tw-cursor-move tw-select-none"
          style={{ opacity: isDragging ? 0.6 : 1 }}
        >
          <div className="tw-flex tw-items-center tw-justify-between">
            <div className="tw-flex-1">
              <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                {comp.name}
              </p>
              <p className="tw-text-xs tw-text-gray-500">
                {comp.placeholders ? comp.placeholders.length : 0} placeholders
              </p>
            </div>
            <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
          </div>
        </div>

        {/* Ant Design Collapse for Preview */}
        <Collapse
          ghost
          size="small"
          className="tw-bg-transparent"
          expandIcon={({ isActive }) => (
            <ChevronDown
              className={`tw-w-3 tw-h-3 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                isActive ? "tw-rotate-180" : ""
              }`}
            />
          )}
          expandIconPosition="end"
          items={[
            {
              key: "preview",
              label: (
                <span className="tw-text-xs tw-text-gray-600">Preview</span>
              ),
              children: <div className="tw-p-2">{previewContent}</div>,
            },
          ]}
        />
      </div>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden tw-relative">
        {/* Mobile Backdrop */}
        {isMobile && (isComponentOpen || isStructureOpen) && (
          <div
            className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-40"
            onClick={() => {
              setIsComponentOpen(false);
              setIsStructureOpen(false);
            }}
          />
        )}

        {/* Left Sidebar - Components Library */}
        <div
          className={`${
            isComponentOpen
              ? isMobile
                ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
                : isTablet
                ? "tw-w-64"
                : "tw-w-[18rem]"
              : "tw-w-0 tw-overflow-hidden"
          } tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
            isMobile && isComponentOpen ? "tw-shadow-2xl" : ""
          }`}
        >
          <div className="tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
            <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
              <Tooltip
                title={
                  isComponentOpen
                    ? "Hide Component List"
                    : "Show Component List"
                }
              >
                <button
                  onClick={() => setIsComponentOpen((v) => !v)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft
                    size={30}
                    className={` ${isComponentOpen ? "" : "tw-rotate-180 "}`}
                  />
                </button>
              </Tooltip>
            </div>
            <div className="tw-p-3 md:tw-p-4 tw-border-gray-200">
              <div className="tw-flex tw-items-center tw-justify-between">
                <div>
                  <h3 className="tw-text-base md:tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-1">
                    Component Library
                  </h3>
                  <p className="tw-text-xs tw-text-gray-600 tw-hidden md:tw-block">
                    Drag components to the canvas to build your page
                  </p>
                </div>
                {isMobile && (
                  <button
                    onClick={() => setIsComponentOpen(false)}
                    className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-gray-600 tw-rounded-lg"
                  >
                    <X className="tw-w-5 tw-h-5" />
                  </button>
                )}
              </div>
            </div>
          </div>

          <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4">
            <Input
              placeholder="Search components..."
              size="middle"
              prefix={
                isSearching ? (
                  <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
                ) : (
                  <Search className="tw-w-4 tw-h-8 tw-text-gray-400" />
                )
              }
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="tw-rounded-lg"
              allowClear
            />
            <Collapse
              ghost
              size="small"
              defaultActiveKey={Object.keys(groupedComponents)}
              className="tw-bg-transparent"
              expandIcon={({ isActive }) => (
                <ChevronDown
                  className={`tw-w-4 tw-h-4 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                    isActive ? "tw-rotate-180" : ""
                  }`}
                />
              )}
              expandIconPosition="end"
              items={Object.entries(groupedComponents).map(
                ([categoryId, categoryData]) => ({
                  key: categoryId,
                  label: (
                    <div className="tw-flex tw-items-center tw-text-center">
                      <div
                        className="tw-w-3 tw-h-3 tw-rounded-full tw-mr-2"
                        style={{ backgroundColor: categoryData.color }}
                      />
                      <span className="tw-font-medium tw-text-gray-900 tw-text-base">
                        {categoryData.name}
                      </span>
                      <span className="tw-ml-2 tw-text-sm tw-text-gray-500">
                        ({categoryData.components.length})
                      </span>
                    </div>
                  ),
                  children: (
                    <div className="tw-space-y-2 tw-ml-2">
                      {categoryData.components.map((component) => (
                        <LibraryItem key={component.id} comp={component} />
                      ))}
                    </div>
                  ),
                })
              )}
            />

            {Object.keys(groupedComponents).length === 0 && (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500">No components available</p>
                <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                  Create components first to use them here
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col">
          <PagePreview
            pageData={pageData}
            setPageData={setPageData}
            components={components}
            handleSave={handleSave}
            saving={saving}
            onCancel={onCancel}
            isStructureOpen={isStructureOpen}
            setIsStructureOpen={setIsStructureOpen}
            isComponentOpen={isComponentOpen}
            setIsComponentOpen={setIsComponentOpen}
          />
        </div>

        <PageStructure
          isStructureOpen={isStructureOpen}
          setIsStructureOpen={setIsStructureOpen}
          pageData={pageData}
          setPageData={setPageData}
          components={components}
          handleCssChange={handleCssChange}
          removeComponentFromPage={removeComponentFromPage}
          moveComponent={moveComponent}
        />
      </div>
    </DndProvider>
  );
};

export default DragDropBuilder;
