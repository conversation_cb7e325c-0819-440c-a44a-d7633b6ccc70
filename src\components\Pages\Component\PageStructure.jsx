import { Input, Tooltip, Collapse, Spin, Select, Radio } from "antd";
import {
  ChevronLeft,
  GripVertical,
  Trash2,
  Search,
  ChevronDown,
  Loader2,
  X,
} from "lucide-react";
import React, { useRef, useState, useEffect } from "react";
import { useDrag, useDrop } from "react-dnd";
import { DND_TYPES } from "../../../util/content";

// const ITEM_TYPE = "STRUCTURE_ITEM";

const PageStructure = ({
  isStructureOpen,
  setIsStructureOpen,
  pageData,
  components,
  handleCssChange,
  removeComponentFromPage,
  moveComponent,
  onComponentFieldChange, // Global onChange handler
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // Debouncing effect for search
  useEffect(() => {
    if (searchTerm) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
        setIsSearching(false);
      }, 300); // 300ms debounce delay

      return () => {
        clearTimeout(timer);
      };
    } else {
      setDebouncedSearchTerm("");
      setIsSearching(false);
    }
  }, [searchTerm]);

  // Responsive detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Simple move function - no debouncing, immediate state update
  //   const moveComponent = useCallback(
  //     (dragIndex, hoverIndex) => {
  //       console.log(`Moving component from ${dragIndex} to ${hoverIndex}`);

  //       setPageData((prevData) => {
  //         const newComponents = [...prevData.components];
  //         const draggedComponent = newComponents[dragIndex];

  //         // Remove the dragged component
  //         newComponents.splice(dragIndex, 1);
  //         // Insert it at the new position
  //         newComponents.splice(hoverIndex, 0, draggedComponent);

  //         console.log(
  //           "New component order:",
  //           newComponents.map((c) => c.name)
  //         );

  //         return {
  //           ...prevData,
  //           components: newComponents,
  //         };
  //       });
  //     },
  //     [setPageData]
  //   );
  // Global onChange handler for all component fields
  const handleGlobalFieldChange = (index, field, value) => {
    if (onComponentFieldChange) {
      onComponentFieldChange(index, field, value);
    }
  };

  // Draggable and droppable structure item component
  const StructureItem = ({ index, componentName, onRemove }) => {
    const ref = useRef(null);
    const componentData = pageData.components[index] || {};

    // useDrop hook for drop target
    const [{ isOver }, drop] = useDrop({
      accept: DND_TYPES.STRUCT_ITEM,
      hover: (item, monitor) => {
        if (!ref.current) {
          return;
        }

        const dragIndex = item.index;
        const hoverIndex = index;

        // Don't replace items with themselves
        if (dragIndex === hoverIndex) {
          return;
        }

        // Determine rectangle on screen
        const hoverBoundingRect = ref.current.getBoundingClientRect();

        // Get vertical middle
        const hoverMiddleY =
          (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

        // Determine mouse position
        const clientOffset = monitor.getClientOffset();
        if (!clientOffset) {
          return;
        }

        // Get pixels to the top
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;

        // Only perform the move when the mouse has crossed half of the items height
        // When dragging downwards, only move when the cursor is below 50%
        // When dragging upwards, only move when the cursor is above 50%

        // Dragging downwards
        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
          return;
        }

        // Dragging upwards
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
          return;
        }

        // Time to actually perform the action
        moveComponent(dragIndex, hoverIndex);

        // Note: we're mutating the monitor item here!
        // Generally it's better to avoid mutations,
        // but it's good here for the sake of performance
        // to avoid expensive index searches.
        item.index = hoverIndex;
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
    });

    // useDrag hook for drag source
    const [{ isDragging }, drag] = useDrag({
      type: DND_TYPES.STRUCT_ITEM,
      item: () => {
        console.log(`Starting drag for item at index: ${index}`);
        return { index };
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Connect drag and drop refs
    drag(drop(ref));

    return (
      <div
        ref={ref}
        className={`tw-flex tw-flex-col tw-p-3 tw-rounded-lg tw-border tw-cursor-move tw-transition-all tw-duration-150 ${
          isDragging
            ? "tw-bg-blue-50 tw-border-blue-300 tw-shadow-lg tw-scale-105"
            : isOver
            ? "tw-bg-yellow-50 tw-border-yellow-300"
            : "tw-bg-gray-50 tw-border-gray-200 tw-hover:tw-bg-gray-100"
        }`}
        style={{
          opacity: isDragging ? 0.8 : 1,
        }}
      >
        {/* Header with drag handle, position, and delete button */}
        <div className="tw-flex tw-items-center tw-justify-between tw-mb-3">
          <div className="tw-flex tw-items-center tw-flex-1">
            <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400 tw-mr-3 tw-flex-shrink-0" />
            <div className="tw-flex-1">
              <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                {componentName || "Unknown Component"}
              </p>
              <p className="tw-text-xs tw-text-gray-500">
                Position {index + 1}
              </p>
            </div>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent drag when clicking remove
              onRemove(index);
            }}
            className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-rounded tw-transition-colors tw-flex-shrink-0"
          >
            <Trash2 className="tw-w-4 tw-h-4" />
          </button>
        </div>

        {/* Form fields */}
        <div className="tw-space-y-3">
          {/* Version Select Dropdown */}
          <div>
            <Select
              size="small"
              placeholder="Select version"
              value={componentData.version || undefined}
              onChange={(value) =>
                handleGlobalFieldChange(index, "version", value)
              }
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              className="tw-w-10"
              options={[
                { value: "v1.0", label: "Version 1.0" },
                { value: "v1.1", label: "Version 1.1" },
                { value: "v2.0", label: "Version 2.0" },
                { value: "v2.1", label: "Version 2.1" },
              ]}
            />
          </div>

          {/* Name Input Field */}
          <div>
            <label className="tw-block tw-text-xs tw-font-medium tw-text-gray-700 tw-mb-1">
              Name
            </label>
            <Input
              size="small"
              placeholder="Enter component name"
              value={componentData.name || ""}
              onChange={(e) =>
                handleGlobalFieldChange(index, "name", e.target.value)
              }
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
            />
          </div>

          {/* Class Input Field */}
          <div>
            <label className="tw-block tw-text-xs tw-font-medium tw-text-gray-700 tw-mb-1">
              CSS Class
            </label>
            <Input
              size="small"
              placeholder="Enter CSS class"
              value={componentData.cssClass || ""}
              onChange={(e) => {
                handleCssChange(index, e.target.value);
                handleGlobalFieldChange(index, "cssClass", e.target.value);
              }}
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
            />
          </div>

          {/* Single or Repeat Radio Buttons */}
          <div>
            <label className="tw-block tw-text-xs tw-font-medium tw-text-gray-700 tw-mb-1">
              Display Type
            </label>
            <Radio.Group
              value={componentData.displayType || "single"}
              onChange={(e) =>
                handleGlobalFieldChange(index, "displayType", e.target.value)
              }
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              size="small"
            >
              <Radio value="single">Single</Radio>
              <Radio value="repeat">Repeat</Radio>
            </Radio.Group>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Right Sidebar - Page Structure (collapsible, animated) */}
      {/* In-layout column with width animation */}
      <div
        className={`tw-bg-white tw-border-l tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
          isStructureOpen
            ? isMobile
              ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
              : isTablet
              ? "tw-w-64"
              : "tw-w-[18rem]"
            : "tw-w-0 tw-overflow-hidden"
        } ${isMobile && isStructureOpen ? "tw-shadow-2xl" : ""}`}
      >
        {isStructureOpen && (
          <>
            <div className=" tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
              <div className="tw-p-3 md:tw-p-4 tw-flex tw-items-start tw-justify-between">
                <div className="tw-flex tw-flex-col tw-justify-start tw-text-start">
                  <h3 className="tw-text-base md:tw-text-lg tw-font-semibold tw-text-gray-900">
                    Page Structure
                  </h3>
                  <p className="tw-text-sm tw-text-gray-600 tw-mt-1 tw-hidden md:tw-block">
                    {pageData?.components?.length || 0} components
                  </p>
                </div>
                {isMobile && (
                  <button
                    onClick={() => setIsStructureOpen(false)}
                    className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-gray-600 tw-rounded-lg tw-ml-2"
                  >
                    <X className="tw-w-5 tw-h-5" />
                  </button>
                )}
              </div>
              <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
                <Tooltip
                  title={
                    isStructureOpen
                      ? "Hide Page Structure"
                      : "Show Page Structure"
                  }
                >
                  <button
                    onClick={() => setIsStructureOpen((v) => !v)}
                    className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                  >
                    <ChevronLeft
                      size={30}
                      className={` ${isStructureOpen ? "" : "tw-rotate-180 "}`}
                    />
                  </button>
                </Tooltip>
              </div>
            </div>
            {/* Search Input */}
            <div className="tw-p-3 md:tw-p-4">
              <Input
                size="middle"
                placeholder="Search components..."
                prefix={
                  isSearching ? (
                    <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
                  ) : (
                    <Search className="tw-w-4 tw-h-8 tw-text-gray-400" />
                  )
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="tw-rounded-lg"
                allowClear
              />
            </div>
          </>
        )}

        <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4 tw-pt-0">
          {pageData?.components?.length > 0 ? (
            <div className="tw-space-y-2">
              {pageData.components
                .map((pageComp, index) => {
                  const component = components?.find(
                    (c) => c.id === pageComp.id
                  );
                  return { pageComp, index, component };
                })
                .filter(({ component }) =>
                  debouncedSearchTerm
                    ? component?.name
                        ?.toLowerCase()
                        .includes(debouncedSearchTerm.toLowerCase())
                    : true
                )
                .map(({ pageComp, index, component }) => (
                  <StructureItem
                    key={
                      pageComp.uniqueId || `fallback-${pageComp.id}-${index}`
                    }
                    index={index}
                    componentName={component?.name}
                    onRemove={removeComponentFromPage}
                  />
                ))}
            </div>
          ) : (
            <div className="tw-text-center tw-py-8">
              <p className="tw-text-gray-500">No components added</p>
              <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                Components will appear here as you add them
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default PageStructure;
